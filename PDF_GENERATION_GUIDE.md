# AfriSure Microinsurance Feasibility Study - PDF Generation Guide

## Overview
This guide provides comprehensive instructions for converting the HTML feasibility study reports into professional, investor-ready PDF documents optimized for A4 printing and digital distribution.

## Files Included
1. `index.html` - Executive Dashboard (Navigation Hub)
2. `market-demand-analysis.html` - Market Demand Analysis Report
3. `regulatory-requirements.html` - Regulatory Requirements Report
4. `competitor-analysis.html` - Competitor Analysis Report
5. `business-financial-model.html` - Business & Financial Model Report
6. `risk-assessment.html` - Risk Assessment & Mitigation Plan
7. `partnership-distribution.html` - Partnership & Distribution Strategy
8. `final-feasibility.html` - Final Feasibility Report & Recommendations
9. `product-manual.html` - Product Manual
10. `operations-finance-manual.html` - Operations & Finance Manual

## Print Optimization Features

### 1. A4 Page Formatting
- **Page Size**: A4 (210mm x 297mm)
- **Margins**: 20mm top/bottom, 15mm left/right, 25mm bottom (for footer)
- **Font Size**: 11pt for body text, 10pt for tables
- **Line Height**: 1.4 for optimal readability

### 2. Headers & Footers
Each page includes:
- **Header**: Document title and study name
- **Footer**: Page numbers, report sequence, author, and confidentiality notice

### 3. Page Break Controls
- **Avoid Breaks**: Charts, tables, key sections, and cards
- **Forced Breaks**: Between major sections where appropriate
- **Print-Safe Colors**: Optimized for both color and black & white printing

## Recommended PDF Generation Methods

### Method 1: Puppeteer (Node.js) - RECOMMENDED
```javascript
const puppeteer = require('puppeteer');

async function generatePDF(htmlFile, outputFile) {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();
  
  await page.goto(`file://${__dirname}/${htmlFile}`, {
    waitUntil: 'networkidle0'
  });
  
  await page.pdf({
    path: outputFile,
    format: 'A4',
    margin: {
      top: '20mm',
      right: '15mm',
      bottom: '25mm',
      left: '15mm'
    },
    printBackground: true,
    displayHeaderFooter: true,
    headerTemplate: '<div></div>',
    footerTemplate: '<div></div>',
    preferCSSPageSize: true
  });
  
  await browser.close();
}

// Generate all PDFs
const files = [
  'index.html',
  'market-demand-analysis.html',
  'regulatory-requirements.html',
  'competitor-analysis.html',
  'business-financial-model.html',
  'risk-assessment.html',
  'partnership-distribution.html',
  'final-feasibility.html',
  'product-manual.html',
  'operations-finance-manual.html'
];

files.forEach(file => {
  const outputName = file.replace('.html', '.pdf');
  generatePDF(file, outputName);
});
```

### Method 2: Chrome/Chromium Browser
1. Open each HTML file in Chrome/Chromium
2. Press `Ctrl+P` (Windows) or `Cmd+P` (Mac)
3. Select "Save as PDF"
4. Choose "More settings"
5. Set:
   - Paper size: A4
   - Margins: Custom (20mm top/bottom, 15mm sides)
   - Options: Background graphics ✓
6. Save each file

### Method 3: wkhtmltopdf
```bash
# Install wkhtmltopdf
# Ubuntu/Debian: sudo apt-get install wkhtmltopdf
# macOS: brew install wkhtmltopdf
# Windows: Download from https://wkhtmltopdf.org/downloads.html

# Generate PDFs
wkhtmltopdf --page-size A4 --margin-top 20mm --margin-right 15mm --margin-bottom 25mm --margin-left 15mm --print-media-type index.html index.pdf

wkhtmltopdf --page-size A4 --margin-top 20mm --margin-right 15mm --margin-bottom 25mm --margin-left 15mm --print-media-type market-demand-analysis.html market-demand-analysis.pdf

# Repeat for all files...
```

### Method 4: Playwright (Alternative to Puppeteer)
```javascript
const { chromium } = require('playwright');

async function generatePDF(htmlFile, outputFile) {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  await page.goto(`file://${__dirname}/${htmlFile}`);
  
  await page.pdf({
    path: outputFile,
    format: 'A4',
    margin: {
      top: '20mm',
      right: '15mm',
      bottom: '25mm',
      left: '15mm'
    },
    printBackground: true
  });
  
  await browser.close();
}
```

## Quality Assurance Checklist

### Before PDF Generation
- [ ] All HTML files open correctly in browser
- [ ] Print preview shows proper page breaks
- [ ] Charts and tables are clearly visible
- [ ] Headers and footers display correctly
- [ ] No content overflow or cut-off sections

### After PDF Generation
- [ ] All pages are A4 size
- [ ] Headers and footers are consistent
- [ ] Page numbers are sequential
- [ ] Charts and graphs are clear and readable
- [ ] Tables fit within page margins
- [ ] No orphaned headers or incomplete sections
- [ ] Colors are appropriate for printing
- [ ] Text is crisp and readable at 100% zoom

## File Naming Convention
Use the following naming convention for generated PDFs:
- `AfriSure_Executive_Dashboard.pdf`
- `AfriSure_Market_Demand_Analysis.pdf`
- `AfriSure_Regulatory_Requirements.pdf`
- `AfriSure_Competitor_Analysis.pdf`
- `AfriSure_Business_Financial_Model.pdf`
- `AfriSure_Risk_Assessment.pdf`
- `AfriSure_Partnership_Distribution.pdf`
- `AfriSure_Final_Feasibility_Report.pdf`
- `AfriSure_Product_Manual.pdf`
- `AfriSure_Operations_Finance_Manual.pdf`

## Distribution Package
Create a complete package including:
1. **Individual PDFs** - Each report as separate PDF
2. **Combined PDF** - All reports merged into single document
3. **Executive Summary** - Key findings and recommendations only
4. **Appendices** - Supporting data and detailed analysis

## Technical Notes

### CSS Print Media Queries
All files include comprehensive `@media print` rules that:
- Optimize layout for A4 paper
- Ensure proper page breaks
- Adjust colors for print compatibility
- Hide navigation elements
- Format headers and footers

### Browser Compatibility
Tested and optimized for:
- Chrome/Chromium 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Print Settings Verification
To verify print settings work correctly:
1. Open any HTML file in browser
2. Use browser's print preview
3. Check that content fits properly on A4 pages
4. Verify headers/footers appear correctly
5. Ensure no content is cut off

## Troubleshooting

### Common Issues
1. **Content Cut Off**: Increase page margins or reduce font size
2. **Poor Page Breaks**: Add `page-break-before: always` to section CSS
3. **Missing Colors**: Enable "Background graphics" in print settings
4. **Blurry Charts**: Use vector graphics or higher DPI settings

### Support
For technical issues with PDF generation, refer to:
- Puppeteer Documentation: https://pptr.dev/
- wkhtmltopdf Manual: https://wkhtmltopdf.org/usage/wkhtmltopdf.txt
- Chrome Print Documentation: https://developer.chrome.com/docs/

## Final Output
The generated PDFs will be professional, investor-ready documents suitable for:
- Board presentations
- Investor meetings
- Regulatory submissions
- Due diligence processes
- Strategic planning sessions

Each PDF maintains consistent branding, professional formatting, and comprehensive content coverage for the AfriSure Microinsurance feasibility study.
