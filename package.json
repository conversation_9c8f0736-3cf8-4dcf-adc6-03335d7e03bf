{"name": "afrisure-feasibility-study-pdf-generator", "version": "1.0.0", "description": "PDF generator for AfriSure Microinsurance Feasibility Study reports", "main": "generate-pdfs.js", "scripts": {"generate-pdfs": "node generate-pdfs.js", "generate-single": "node generate-single-pdf.js", "generate-combined": "node generate-combined-pdf.js", "install-deps": "npm install"}, "dependencies": {"puppeteer": "^21.5.2", "pdf-lib": "^1.17.1", "fs-extra": "^11.1.1"}, "keywords": ["pdf", "puppeteer", "feasibility-study", "microinsurance", "afrisure"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT"}