# AfriSure Feasibility Study - PDF Generation

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

**Windows:**
```bash
# Double-click or run in Command Prompt
install-and-generate.bat
```

**Mac/Linux:**
```bash
# Make executable and run
chmod +x install-and-generate.sh
./install-and-generate.sh
```

### Option 2: Manual Setup

1. **Install Node.js** (if not already installed)
   - Download from: https://nodejs.org/
   - Choose LTS version

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Generate All PDFs**
   ```bash
   npm run generate-pdfs
   ```

## 📋 Available Commands

### Generate All PDFs
```bash
npm run generate-pdfs
# or
node generate-pdfs.js
```

### Generate Single PDF
```bash
npm run generate-single
# or
node generate-single-pdf.js index.html
```

### Generate Combined PDF
```bash
npm run generate-combined
# or
node generate-combined-pdf.js
```

## 📁 Output Structure

After generation, you'll find:

```
pdfs/
├── AfriSure_Executive_Dashboard.pdf
├── AfriSure_Market_Demand_Analysis.pdf
├── AfriSure_Regulatory_Requirements.pdf
├── AfriSure_Competitor_Analysis.pdf
├── AfriSure_Business_Financial_Model.pdf
├── AfriSure_Risk_Assessment.pdf
├── AfriSure_Partnership_Distribution.pdf
├── AfriSure_Final_Feasibility_Report.pdf
├── AfriSure_Product_Manual.pdf
├── AfriSure_Operations_Finance_Manual.pdf
└── AfriSure_Complete_Feasibility_Study.pdf (combined)
```

## 🎯 PDF Specifications

- **Format**: A4 (210mm × 297mm)
- **Margins**: 20mm top/bottom, 15mm left/right, 25mm bottom
- **Quality**: High-resolution, print-ready
- **Features**: Headers, footers, page numbers, professional formatting

## 🔧 Troubleshooting

### Common Issues

**1. "npm not found"**
- Install Node.js from https://nodejs.org/

**2. "Permission denied" (Mac/Linux)**
```bash
chmod +x install-and-generate.sh
```

**3. "HTML file not found"**
- Ensure all HTML files are in the same directory as the scripts

**4. "Puppeteer download failed"**
```bash
npm config set puppeteer_download_host=https://npm.taobao.org/mirrors
npm install puppeteer
```

### Memory Issues
If you encounter memory issues with large files:
```bash
node --max-old-space-size=4096 generate-pdfs.js
```

## 📊 Generation Process

The script will:
1. ✅ Validate all HTML files exist
2. 📁 Create `pdfs` output directory
3. 🔄 Process each HTML file sequentially
4. 📄 Generate high-quality A4 PDFs
5. 📋 Provide detailed summary report

## 🎨 Customization

### Modify PDF Settings
Edit `generate-pdfs.js` and adjust the `config` object:

```javascript
const config = {
  outputDir: './pdfs',
  format: 'A4',
  margin: {
    top: '20mm',
    right: '15mm',
    bottom: '25mm',
    left: '15mm'
  },
  // ... other settings
};
```

### Add New Files
Add to the `files` array in `generate-pdfs.js`:

```javascript
{
  html: 'new-report.html',
  pdf: 'AfriSure_New_Report.pdf',
  title: 'New Report Title'
}
```

## 📈 Performance

- **Average time per PDF**: 3-5 seconds
- **Total generation time**: ~30-50 seconds for all files
- **Memory usage**: ~200-400MB during generation
- **Output size**: ~500KB - 2MB per PDF

## 🔒 Security & Privacy

- All processing is done locally
- No data sent to external servers
- Generated PDFs contain no tracking
- Source HTML files remain unchanged

## 📞 Support

If you encounter issues:

1. Check the console output for specific error messages
2. Verify all HTML files are present
3. Ensure Node.js and npm are properly installed
4. Try generating a single PDF first to isolate issues

## 🎉 Success Indicators

You'll know it worked when you see:
- ✅ "All PDFs generated successfully!"
- 📁 PDFs folder with all 10+ files
- 📊 File size summary in console
- 🎯 No error messages

Ready to generate professional, investor-ready PDFs! 🚀
