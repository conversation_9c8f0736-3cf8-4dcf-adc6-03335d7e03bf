const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs-extra');

// Configuration
const config = {
  outputDir: './pdfs',
  format: 'A4',
  margin: {
    top: '20mm',
    right: '15mm',
    bottom: '25mm',
    left: '15mm'
  },
  printBackground: true,
  displayHeaderFooter: false, // We use CSS headers/footers
  preferCSSPageSize: true,
  timeout: 30000 // 30 seconds timeout
};

// List of all HTML files to convert
const files = [
  { 
    html: 'index.html', 
    pdf: 'AfriSure_Executive_Dashboard.pdf',
    title: 'Executive Dashboard'
  },
  { 
    html: 'market-demand-analysis.html', 
    pdf: 'AfriSure_Market_Demand_Analysis.pdf',
    title: 'Market Demand Analysis'
  },
  { 
    html: 'regulatory-requirements.html', 
    pdf: 'AfriSure_Regulatory_Requirements.pdf',
    title: 'Regulatory Requirements'
  },
  { 
    html: 'competitor-analysis.html', 
    pdf: 'AfriSure_Competitor_Analysis.pdf',
    title: 'Competitor Analysis'
  },
  { 
    html: 'business-financial-model.html', 
    pdf: 'AfriSure_Business_Financial_Model.pdf',
    title: 'Business & Financial Model'
  },
  { 
    html: 'risk-assessment.html', 
    pdf: 'AfriSure_Risk_Assessment.pdf',
    title: 'Risk Assessment & Mitigation'
  },
  { 
    html: 'partnership-distribution.html', 
    pdf: 'AfriSure_Partnership_Distribution.pdf',
    title: 'Partnership & Distribution Strategy'
  },
  { 
    html: 'final-feasibility.html', 
    pdf: 'AfriSure_Final_Feasibility_Report.pdf',
    title: 'Final Feasibility Report'
  },
  { 
    html: 'product-manual.html', 
    pdf: 'AfriSure_Product_Manual.pdf',
    title: 'Product Manual'
  },
  { 
    html: 'operations-finance-manual.html', 
    pdf: 'AfriSure_Operations_Finance_Manual.pdf',
    title: 'Operations & Finance Manual'
  }
];

/**
 * Generate a single PDF from HTML file
 */
async function generatePDF(htmlFile, outputFile, title) {
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Set viewport for consistent rendering
    await page.setViewport({ width: 1200, height: 800 });
    
    // Check if HTML file exists
    const htmlPath = path.resolve(htmlFile);
    if (!fs.existsSync(htmlPath)) {
      throw new Error(`HTML file not found: ${htmlPath}`);
    }
    
    console.log(`📄 Processing: ${title}`);
    console.log(`   Source: ${htmlFile}`);
    console.log(`   Output: ${outputFile}`);
    
    // Load the HTML file
    await page.goto(`file://${htmlPath}`, {
      waitUntil: 'networkidle0',
      timeout: config.timeout
    });
    
    // Wait a bit more for any dynamic content
    await page.waitForTimeout(2000);
    
    // Generate PDF with optimized settings
    await page.pdf({
      path: path.join(config.outputDir, outputFile),
      format: config.format,
      margin: config.margin,
      printBackground: config.printBackground,
      displayHeaderFooter: config.displayHeaderFooter,
      preferCSSPageSize: config.preferCSSPageSize
    });
    
    console.log(`✅ Generated: ${outputFile}`);
    
  } catch (error) {
    console.error(`❌ Error generating ${title}:`, error.message);
    throw error;
  } finally {
    await browser.close();
  }
}

/**
 * Generate all PDFs
 */
async function generateAllPDFs() {
  console.log('🚀 Starting AfriSure Feasibility Study PDF Generation');
  console.log('=' .repeat(60));
  
  // Create output directory
  await fs.ensureDir(config.outputDir);
  console.log(`📁 Output directory: ${path.resolve(config.outputDir)}`);
  console.log('');
  
  const startTime = Date.now();
  let successCount = 0;
  let errorCount = 0;
  
  // Generate PDFs sequentially to avoid memory issues
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    try {
      console.log(`[${i + 1}/${files.length}] ${file.title}`);
      await generatePDF(file.html, file.pdf, file.title);
      successCount++;
      console.log('');
    } catch (error) {
      console.error(`Failed to generate ${file.title}: ${error.message}`);
      errorCount++;
      console.log('');
    }
  }
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  console.log('=' .repeat(60));
  console.log('📊 GENERATION SUMMARY');
  console.log('=' .repeat(60));
  console.log(`✅ Successful: ${successCount} PDFs`);
  console.log(`❌ Failed: ${errorCount} PDFs`);
  console.log(`⏱️  Total time: ${duration} seconds`);
  console.log(`📁 Output location: ${path.resolve(config.outputDir)}`);
  
  if (successCount > 0) {
    console.log('');
    console.log('📋 Generated Files:');
    files.forEach(file => {
      const filePath = path.join(config.outputDir, file.pdf);
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        const sizeKB = (stats.size / 1024).toFixed(1);
        console.log(`   • ${file.pdf} (${sizeKB} KB)`);
      }
    });
  }
  
  if (errorCount === 0) {
    console.log('');
    console.log('🎉 All PDFs generated successfully!');
    console.log('📧 Ready for investor distribution and regulatory submission.');
  } else {
    console.log('');
    console.log('⚠️  Some PDFs failed to generate. Check the errors above.');
  }
}

/**
 * Validate environment before starting
 */
async function validateEnvironment() {
  console.log('🔍 Validating environment...');
  
  // Check if all HTML files exist
  const missingFiles = [];
  for (const file of files) {
    if (!fs.existsSync(file.html)) {
      missingFiles.push(file.html);
    }
  }
  
  if (missingFiles.length > 0) {
    console.error('❌ Missing HTML files:');
    missingFiles.forEach(file => console.error(`   • ${file}`));
    process.exit(1);
  }
  
  console.log('✅ All HTML files found');
  console.log('✅ Environment validation passed');
  console.log('');
}

// Main execution
async function main() {
  try {
    await validateEnvironment();
    await generateAllPDFs();
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
AfriSure Feasibility Study PDF Generator

Usage:
  node generate-pdfs.js              Generate all PDFs
  node generate-pdfs.js --help       Show this help message

Output:
  All PDFs will be saved to the './pdfs' directory

Requirements:
  - All HTML files must be in the current directory
  - Node.js and Puppeteer must be installed
  `);
  process.exit(0);
}

// Run the generator
if (require.main === module) {
  main();
}

module.exports = { generatePDF, generateAllPDFs, files, config };
