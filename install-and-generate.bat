@echo off
echo ========================================
echo AfriSure Feasibility Study PDF Generator
echo ========================================
echo.

echo 📦 Installing dependencies...
call npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo ✅ Dependencies installed successfully!
echo.

echo 🚀 Generating PDFs...
call npm run generate-pdfs

if %errorlevel% neq 0 (
    echo ❌ Failed to generate PDFs
    pause
    exit /b 1
)

echo.
echo 🎉 PDF generation completed!
echo 📁 Check the 'pdfs' folder for your generated files.
echo.
pause
