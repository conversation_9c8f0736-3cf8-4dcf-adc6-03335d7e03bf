#!/bin/bash

echo "========================================"
echo "AfriSure Feasibility Study PDF Generator"
echo "========================================"
echo

echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo
echo "✅ Dependencies installed successfully!"
echo

echo "🚀 Generating PDFs..."
npm run generate-pdfs

if [ $? -ne 0 ]; then
    echo "❌ Failed to generate PDFs"
    exit 1
fi

echo
echo "🎉 PDF generation completed!"
echo "📁 Check the 'pdfs' folder for your generated files."
echo
