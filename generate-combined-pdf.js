const puppeteer = require('puppeteer');
const { PDFDocument } = require('pdf-lib');
const fs = require('fs-extra');
const path = require('path');
const { files } = require('./generate-pdfs');

/**
 * Generate a combined PDF with all reports
 */
async function generateCombinedPDF() {
  console.log('🚀 Generating Combined AfriSure Feasibility Study PDF');
  console.log('=' .repeat(60));
  
  const outputDir = './pdfs';
  const combinedPdfPath = path.join(outputDir, 'AfriSure_Complete_Feasibility_Study.pdf');
  
  // Ensure output directory exists
  await fs.ensureDir(outputDir);
  
  try {
    // Create a new PDF document
    const combinedPdf = await PDFDocument.create();
    
    console.log('📄 Processing individual reports...');
    
    // Generate individual PDFs first if they don't exist
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const pdfPath = path.join(outputDir, file.pdf);
      
      console.log(`[${i + 1}/${files.length}] ${file.title}`);
      
      // Generate PDF if it doesn't exist
      if (!fs.existsSync(pdfPath)) {
        console.log(`   Generating: ${file.pdf}`);
        
        const page = await browser.newPage();
        await page.setViewport({ width: 1200, height: 800 });
        
        const htmlPath = path.resolve(file.html);
        await page.goto(`file://${htmlPath}`, {
          waitUntil: 'networkidle0',
          timeout: 30000
        });
        
        await page.waitForTimeout(2000);
        
        await page.pdf({
          path: pdfPath,
          format: 'A4',
          margin: {
            top: '20mm',
            right: '15mm',
            bottom: '25mm',
            left: '15mm'
          },
          printBackground: true,
          displayHeaderFooter: false,
          preferCSSPageSize: true
        });
        
        await page.close();
      }
      
      // Read the PDF and add to combined document
      console.log(`   Adding to combined PDF: ${file.pdf}`);
      const pdfBytes = await fs.readFile(pdfPath);
      const pdf = await PDFDocument.load(pdfBytes);
      const pages = await combinedPdf.copyPages(pdf, pdf.getPageIndices());
      
      pages.forEach((page) => combinedPdf.addPage(page));
    }
    
    await browser.close();
    
    // Save the combined PDF
    console.log('💾 Saving combined PDF...');
    const combinedPdfBytes = await combinedPdf.save();
    await fs.writeFile(combinedPdfPath, combinedPdfBytes);
    
    // Get file stats
    const stats = fs.statSync(combinedPdfPath);
    const sizeMB = (stats.size / (1024 * 1024)).toFixed(2);
    const pageCount = combinedPdf.getPageCount();
    
    console.log('=' .repeat(60));
    console.log('✅ Combined PDF generated successfully!');
    console.log(`📁 Location: ${path.resolve(combinedPdfPath)}`);
    console.log(`📊 Size: ${sizeMB} MB`);
    console.log(`📄 Pages: ${pageCount}`);
    console.log('');
    console.log('📋 Contents:');
    files.forEach((file, index) => {
      console.log(`   ${index + 1}. ${file.title}`);
    });
    
  } catch (error) {
    console.error('❌ Error generating combined PDF:', error.message);
    throw error;
  }
}

if (require.main === module) {
  generateCombinedPDF().catch(console.error);
}

module.exports = { generateCombinedPDF };
