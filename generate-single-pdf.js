const { generatePDF, files } = require('./generate-pdfs');
const path = require('path');
const fs = require('fs-extra');

/**
 * Generate a single PDF file
 * Usage: node generate-single-pdf.js <filename>
 * Example: node generate-single-pdf.js index.html
 */

async function generateSinglePDF() {
  const filename = process.argv[2];
  
  if (!filename) {
    console.log('Usage: node generate-single-pdf.js <filename>');
    console.log('');
    console.log('Available files:');
    files.forEach(file => {
      console.log(`  • ${file.html} → ${file.pdf}`);
    });
    process.exit(1);
  }
  
  // Find the file configuration
  const fileConfig = files.find(f => f.html === filename);
  
  if (!fileConfig) {
    console.error(`❌ File not found in configuration: ${filename}`);
    console.log('');
    console.log('Available files:');
    files.forEach(file => {
      console.log(`  • ${file.html}`);
    });
    process.exit(1);
  }
  
  // Create output directory
  const outputDir = './pdfs';
  await fs.ensureDir(outputDir);
  
  try {
    console.log(`🚀 Generating single PDF: ${fileConfig.title}`);
    console.log('=' .repeat(50));
    
    const startTime = Date.now();
    await generatePDF(fileConfig.html, fileConfig.pdf, fileConfig.title);
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    const outputPath = path.join(outputDir, fileConfig.pdf);
    const stats = fs.statSync(outputPath);
    const sizeKB = (stats.size / 1024).toFixed(1);
    
    console.log('=' .repeat(50));
    console.log('✅ PDF generated successfully!');
    console.log(`📁 Location: ${path.resolve(outputPath)}`);
    console.log(`📊 Size: ${sizeKB} KB`);
    console.log(`⏱️  Time: ${duration} seconds`);
    
  } catch (error) {
    console.error('❌ Error generating PDF:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  generateSinglePDF();
}
